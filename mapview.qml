import QtQuick 2.15
import QtLocation 5.15
import QtPositioning 5.15

Rectangle {
    id: mapContainer
    width: 800
    height: 600
    
    property var measurePoints: []
    property bool measureMode: false
    
    // 信号定义
    signal moveToBeijing()
    signal startMeasure()
    
    Plugin {
        id: mapPlugin
        name: "osm" // 使用OpenStreetMap
        PluginParameter {
            name: "osm.mapping.providersrepository.disabled"
            value: "true"
        }
        PluginParameter {
            name: "osm.mapping.providersrepository.address"
            value: "http://maps-redirect.qt.io/osm/5.6/"
        }
    }
    
    Map {
        id: map
        anchors.fill: parent
        plugin: mapPlugin
        center: QtPositioning.coordinate(39.9042, 116.4074) // 北京坐标
        zoomLevel: 10
        
        // 处理鼠标点击事件
        MouseArea {
            anchors.fill: parent
            onClicked: {
                if (measureMode) {
                    var coord = map.toCoordinate(Qt.point(mouse.x, mouse.y))
                    measurePoints.push(coord)
                    
                    if (measurePoints.length === 1) {
                        // 添加第一个点标记
                        var firstMarker = Qt.createComponent("qrc:/PointMarker.qml")
                        if (firstMarker.status === Component.Ready) {
                            var marker1 = firstMarker.createObject(map, {
                                "coordinate": coord,
                                "text": "起点"
                            })
                            map.addMapItem(marker1)
                        }
                        statusText.text = "请点击第二个点"
                    } else if (measurePoints.length === 2) {
                        // 添加第二个点标记
                        var secondMarker = Qt.createComponent("qrc:/PointMarker.qml")
                        if (secondMarker.status === Component.Ready) {
                            var marker2 = secondMarker.createObject(map, {
                                "coordinate": coord,
                                "text": "终点"
                            })
                            map.addMapItem(marker2)
                        }
                        
                        // 计算距离
                        var distance = measurePoints[0].distanceTo(measurePoints[1])
                        var distanceKm = (distance / 1000).toFixed(2)
                        
                        // 添加连线
                        var line = Qt.createComponent("qrc:/MeasureLine.qml")
                        if (line.status === Component.Ready) {
                            var polyline = line.createObject(map, {
                                "path": measurePoints
                            })
                            map.addMapItem(polyline)
                        }
                        
                        statusText.text = "距离: " + distanceKm + " 公里"
                        measureMode = false
                        measurePoints = []
                    }
                }
            }
        }
    }
    
    // 状态文本
    Rectangle {
        id: statusBar
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        height: 40
        color: "lightgray"
        opacity: 0.8
        
        Text {
            id: statusText
            anchors.centerIn: parent
            text: "地图已加载"
            font.pixelSize: 14
        }
    }
    
    // 清除按钮
    Rectangle {
        id: clearButton
        anchors.top: parent.top
        anchors.right: parent.right
        anchors.margins: 10
        width: 80
        height: 30
        color: "lightblue"
        border.color: "blue"
        radius: 5
        
        Text {
            anchors.centerIn: parent
            text: "清除"
            font.pixelSize: 12
        }
        
        MouseArea {
            anchors.fill: parent
            onClicked: {
                // 清除所有地图项
                map.clearMapItems()
                measurePoints = []
                measureMode = false
                statusText.text = "地图已清除"
            }
        }
    }
    
    // 响应外部信号
    function moveToBeijingLocation() {
        map.center = QtPositioning.coordinate(39.9042, 116.4074)
        map.zoomLevel = 12
        statusText.text = "已移动到北京"
    }
    
    function startMeasureMode() {
        measureMode = true
        measurePoints = []
        map.clearMapItems()
        statusText.text = "测距模式：请点击第一个点"
    }
}
